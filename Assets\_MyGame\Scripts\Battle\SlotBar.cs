using System;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;

public class SlotBar : MonoBehaviour
{
    public Action<bool> OnGameDone;
    public int useSlotCout = 7;
    public Slot[] slots;
    private bool isGameOver;

    private MergeRuleManager ruleManager;
    public void Init(int lockCount)
    {
        ruleManager = new MergeRuleManager();
        var count = slots.Length;

        var videoCount = 0;
        var shareIndex = lockCount == 1 ? count - 2 : count - 3;
        for (int i = count - 2; i > count - 2 - lockCount; i--)
        {
            var slot = slots[i];
            bool isUseShare = false;
            if (GameGlobal.OpenSlotIsUseShare && i == shareIndex)
            {
                isUseShare = true;
            }
            if (!isUseShare)
                videoCount++;
            ChangeSlotLock(slot, true, isUseShare);
        }
        if (videoCount > 0)
        {
            FlSdk.Inst.ReportVideoExposure(videoCount);
        }

        UpdateHelpSlotPos();
    }
    public Slot GetFirstLockSlot()
    {
        for (int i = 0; i < slots.Length - 1; i++)
        {
            var slot = slots[i];
            if (slot.IsLock)
            {
                return slot;
            }
        }
        return null;
    }

    private void UpdateHelpSlotPos()
    {
        useSlotCout = 0;
        var slotCount = slots.Length;
        var helpSlot = slots[slotCount - 1];
        var isSetHelpSlot = false;
        for (int i = 0; i < slotCount - 1; i++)
        {
            var slot = slots[i];
            if (!slot.IsLock)
            {
                useSlotCout++;
            }
            if (!isSetHelpSlot && slot.IsLock)
            {
                var preSlot = slots[i - 1];
                isSetHelpSlot = true;
                helpSlot.transform.position = preSlot.transform.position;
            }
        }

        if (!isSetHelpSlot)
        {
            helpSlot.transform.position = slots[slotCount - 2].transform.position;
        }
    }

    internal void UnLockSlot()
    {
        for (int i = 0; i < slots.Length - 1; i++)
        {
            var slot = slots[i];
            if (slot.IsLock)
            {
                ChangeSlotLock(slot, false);
                break;
            }
        }
        UpdateHelpSlotPos();
    }
    internal void ChangeSlotLock(Slot slot, bool isLock, bool isUseShare = false)
    {
        var lockGo = slot.transform.Find("lock");
        if (lockGo != null)
        {
            lockGo.gameObject.SetActive(isLock);

            if (isUseShare && lockGo.TryGetComponent<SpriteRenderer>(out var sprite))
            {
                AssetBundleManager.LoadSprite("Textures/lockSlotShare", (obj) =>
                {
                    if (this == null)
                        return;
                    if (obj != null)
                    {
                        sprite.sprite = obj;
                    }
                });
            }
        }
        slot.IsLock = isLock;
        slot.IsOpenByShare = isUseShare;
    }

    internal void Add(MaJiang maJiang)
    {
        if (isGameOver || IsFull)
            return;

        var insterIndex = ruleManager.FindInsertPosition(slots, useSlotCout, maJiang.Num);
        if (insterIndex >= useSlotCout)
            return;

        if (insterIndex == -1)
        {
            GameOver();
            return;
        }

        BattleScene.Inst.RemoveMajiang(maJiang.GetHashCode());
        MajiangMoveBackward(insterIndex, 0.15f);
        MajiangTable2Slot(maJiang, insterIndex);
        MajiangMove2Left();
    }

    public void CheckWin()
    {
        if (BattleScene.Inst.MajinagIsClear && IsClear)
        {
            GameWin();
        }
    }
    private void GameWin()
    {
        if (isGameOver)
            return;
        isGameOver = true;
        OnGameDone?.Invoke(true);
    }

    private void GameOver()
    {
        if (isGameOver)
            return;
        isGameOver = true;
        OnGameDone?.Invoke(false);
    }

    private void MajiangMoveBackward(int insterIndex, float moveDuration)
    {
        if (slots[insterIndex].IsEmpty)
            return;

        float delay = 0;
        // for (int i = slots.Length - 2; i >= insterIndex; i--)
        for (int i = useSlotCout - 2; i >= insterIndex; i--)
        {
            var slot = slots[i];
            if (slot.IsEmpty)
                continue;

            var targetSlot = slots[i + 1];
            var majiang = slot.GetMajiang();
            slot.ClearMajiang();
            targetSlot.Add(majiang);
            majiang.JumpToSlot(targetSlot, moveDuration, delay, () =>
            {
                majiang.PunchMove(-0.05f, 0.2f, 8, 0f);
                targetSlot.PunchMove(-0.05f, 0.2f, 8, 0f);
            });
            delay += 0.1f;
        }
    }

    private void MajiangMove2Left()
    {
        var firstEmptyIndex = -1;
        float delay = 0;
        for (int i = 0; i < slots.Length; i++)
        {
            var slot = slots[i];
            if (slot.IsEmpty)
            {
                if (firstEmptyIndex == -1)
                {
                    firstEmptyIndex = i;
                }
            }
            else
            {
                if (firstEmptyIndex == -1)
                    continue;

                var targetSlot = slots[firstEmptyIndex];
                var majiang = slot.GetMajiang();
                slot.ClearMajiang();
                targetSlot.Add(majiang);

                //每个空槽位都跳一下
                int startIndex = i;
                int endIndex = firstEmptyIndex;
                float jumpDuration = 0.15f;
                float totalDelay = delay;
                delay += 0.02f;

                if (startIndex > endIndex + 1)
                {
                    for (int j = startIndex - 1; j >= endIndex; j--)
                    {
                        var intermediateSlot = slots[j];
                        if (j == endIndex)
                        {
                            majiang.JumpToSlot(intermediateSlot, jumpDuration, totalDelay, () =>
                            {
                                majiang.PunchMove(-0.05f, 0.2f, 8, 0f);
                                targetSlot.PunchMove(-0.05f, 0.2f, 8, 0f);
                            });
                        }
                        else
                        {
                            majiang.JumpToSlot(intermediateSlot, jumpDuration, totalDelay, null);
                        }
                    }
                }
                else
                {
                    majiang.JumpToSlot(targetSlot, jumpDuration, totalDelay, () =>
                    {
                        majiang.PunchMove(-0.05f, 0.2f, 8, 0f);
                        targetSlot.PunchMove(-0.05f, 0.2f, 8, 0f);
                    });
                }

                firstEmptyIndex++;
            }
        }
    }

    private void MajiangTable2Slot(MaJiang majiang, int insterIndex)
    {
        var targetSlot = slots[insterIndex];
        targetSlot.Add(majiang);
        targetSlot.UnReadyMerge = true;

        var pos = majiang.transform.position;
        var normalizedValue = Mathf.InverseLerp(-2, 2, pos.z - BattleScene.Inst.bornAreaPos.z);
        var moveDuration = Mathf.Lerp(0.3f, 0.5f, normalizedValue);

        majiang.FlyToSlot(targetSlot, moveDuration, (isFullTween) =>
        {
            if (isFullTween)
            {
                majiang.PunchMove(-0.1f, 0.4f, 8, 0.8f);
                targetSlot.PunchMove(-0.1f, 0.4f, 8, 0.8f);
            }
            targetSlot.UnReadyMerge = false;
            CheckMajiangMerge();
        });
    }

    private void CheckMajiangMerge()
    {
        var skipCheckGameOver = false;
        for (int i = 2; i < useSlotCout; i++)
        {
            var leftSlot = slots[i - 2];
            var centerSlot = slots[i - 1];
            var rightSlot = slots[i];

            if (rightSlot.IsEmpty)
                continue;


            if (leftSlot.UnReadyMerge || centerSlot.UnReadyMerge || rightSlot.UnReadyMerge)
            {
                skipCheckGameOver = true;
                continue;
            }

            bool canMerge = ruleManager.CanMerge(leftSlot, centerSlot, rightSlot);
            if (canMerge)
            {
                skipCheckGameOver = true;
                MaJiang.PlayMerge(leftSlot, centerSlot, rightSlot, () =>
                {
                    MajiangMove2Left();
                    CheckWin();
                });
                break;
            }
        }

        if (!skipCheckGameOver && IsFull)
        {
            GameOver();
        }
    }

    internal bool IsFull
    {
        get
        {
            for (int i = 0; i < useSlotCout; i++)
            {
                if (slots[i].IsEmpty)
                    return false;
            }
            return true;
        }
    }
    internal bool IsClear
    {
        get
        {
            for (int i = 0; i < useSlotCout; i++)
            {
                if (!slots[i].IsEmpty)
                    return false;
            }
            return true;
        }
    }
    internal (int, int, int) GetMaxCountNumAndCountAndEmptyCount()
    {
        // if (slots[0].Num == 0)
        // {
        //     return (0, 0, useSlotCout);
        // }

        var emptyCount = 0;
        var numCounts = new Dictionary<int, int>();
        for (int i = 0; i < useSlotCout; i++)
        {
            var slot = slots[i];
            if (slot.Num == 0)
            {
                emptyCount++;
                continue;
            }

            if (numCounts.ContainsKey(slot.Num))
            {
                numCounts[slot.Num]++;
            }
            else
            {
                numCounts[slot.Num] = 1;
            }
        }

        var maxNum = 0;
        var maxCount = 0;
        foreach (var item in numCounts)
        {
            if (maxCount < item.Value)
            {
                maxNum = item.Key;
                maxCount = item.Value;
            }
        }
        return (maxNum, maxCount, emptyCount);
    }

    internal void Clear2Table()
    {
        isGameOver = false;
        for (int i = 0; i < slots.Length; i++)
        {
            var slot = slots[i];
            if (slot.IsEmpty)
                continue;
            var majiang = slot.GetMajiang();
            BattleScene.Inst.AddMajiang(majiang);

            slot.MoveMajiang2Table();
        }
    }
}